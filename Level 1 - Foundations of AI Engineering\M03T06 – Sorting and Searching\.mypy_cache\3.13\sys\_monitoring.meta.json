{"data_mtime": 1758587252, "dep_lines": [8, 9, 10, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "types", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "6538719a3d28b7c044c8633b6b63602a46909534", "id": "sys._monitoring", "ignore_all": true, "interface_hash": "c74239099680e97e8115a6619f4e27158e9ed0d6", "mtime": 1756068579, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\sys\\_monitoring.pyi", "plugin_data": null, "size": 1492, "suppressed": [], "version_id": "1.15.0"}