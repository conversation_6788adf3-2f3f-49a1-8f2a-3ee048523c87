{".class": "MypyFile", "_fullname": "email.errors", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BoundaryError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageParseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.BoundaryError", "name": "BoundaryError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.BoundaryError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.BoundaryError", "email.errors.MessageParseError", "email.errors.MessageError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CharsetError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.CharsetError", "name": "<PERSON><PERSON>t<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.CharsetError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.CharsetError", "email.errors.MessageError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CloseBoundaryNotFoundDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.CloseBoundaryNotFoundDefect", "name": "CloseBoundaryNotFoundDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.CloseBoundaryNotFoundDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.CloseBoundaryNotFoundDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FirstHeaderLineIsContinuationDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.FirstHeaderLineIsContinuationDefect", "name": "FirstHeaderLineIsContinuationDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.FirstHeaderLineIsContinuationDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.FirstHeaderLineIsContinuationDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.HeaderDefect", "name": "HeaderDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.HeaderDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderMissingRequiredValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.HeaderDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.HeaderMissingRequiredValue", "name": "HeaderMissingRequiredValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.HeaderMissingRequiredValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.HeaderMissingRequiredValue", "email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderParseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageParseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.HeaderParseError", "name": "Header<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.HeaderParseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.HeaderParseError", "email.errors.MessageParseError", "email.errors.MessageError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderWriteError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.HeaderWriteError", "name": "HeaderWriteError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.HeaderWriteError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.HeaderWriteError", "email.errors.MessageError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidBase64CharactersDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.InvalidBase64CharactersDefect", "name": "InvalidBase64CharactersDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.InvalidBase64CharactersDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.InvalidBase64CharactersDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidBase64LengthDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.InvalidBase64LengthDefect", "name": "InvalidBase64LengthDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.InvalidBase64LengthDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.InvalidBase64LengthDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidBase64PaddingDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.InvalidBase64PaddingDefect", "name": "InvalidBase64PaddingDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.InvalidBase64PaddingDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.InvalidBase64PaddingDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidDateDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.HeaderDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.InvalidDateDefect", "name": "InvalidDateDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.InvalidDateDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.InvalidDateDefect", "email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeaderDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.HeaderDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.InvalidHeaderDefect", "name": "InvalidHeaderDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.InvalidHeaderDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.InvalidHeaderDefect", "email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidMultipartContentTransferEncodingDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.InvalidMultipartContentTransferEncodingDefect", "name": "InvalidMultipartContentTransferEncodingDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.InvalidMultipartContentTransferEncodingDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.InvalidMultipartContentTransferEncodingDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MalformedHeaderDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "email.errors.MalformedHeaderDefect", "line": 29, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "email.errors.MissingHeaderBodySeparatorDefect"}}, "MessageDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MessageDefect", "name": "MessageDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MessageDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.errors.MessageDefect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "line"], "arg_types": ["email.errors.MessageDefect", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MessageDefect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MessageError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MessageError", "name": "MessageError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MessageError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MessageError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MessageParseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MessageParseError", "name": "MessageParseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MessageParseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MessageParseError", "email.errors.MessageError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MisplacedEnvelopeHeaderDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MisplacedEnvelopeHeaderDefect", "name": "MisplacedEnvelopeHeaderDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MisplacedEnvelopeHeaderDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MisplacedEnvelopeHeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingHeaderBodySeparatorDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MissingHeaderBodySeparatorDefect", "name": "MissingHeaderBodySeparatorDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MissingHeaderBodySeparatorDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MissingHeaderBodySeparatorDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartConversionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageError", "builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MultipartConversionError", "name": "MultipartConversionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MultipartConversionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MultipartConversionError", "email.errors.MessageError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartInvariantViolationDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.MultipartInvariantViolationDefect", "name": "MultipartInvariantViolationDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.MultipartInvariantViolationDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.MultipartInvariantViolationDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoBoundaryInMultipartDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.NoBoundaryInMultipartDefect", "name": "NoBoundaryInMultipartDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.NoBoundaryInMultipartDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.NoBoundaryInMultipartDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NonASCIILocalPartDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.HeaderDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.NonASCIILocalPartDefect", "name": "NonASCIILocalPartDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.NonASCIILocalPartDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.NonASCIILocalPartDefect", "email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NonPrintableDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.HeaderDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.NonPrintableDefect", "name": "NonPrintableDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.NonPrintableDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.NonPrintableDefect", "email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "non_printables"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email.errors.NonPrintableDefect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "non_printables"], "arg_types": ["email.errors.NonPrintableDefect", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NonPrintableDefect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObsoleteHeaderDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.HeaderDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.ObsoleteHeaderDefect", "name": "ObsoleteHeaderDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.ObsoleteHeaderDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.ObsoleteHeaderDefect", "email.errors.HeaderDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StartBoundaryNotFoundDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.StartBoundaryNotFoundDefect", "name": "StartBoundaryNotFoundDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.StartBoundaryNotFoundDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.StartBoundaryNotFoundDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UndecodableBytesDefect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["email.errors.MessageDefect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email.errors.UndecodableBytesDefect", "name": "UndecodableBytesDefect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email.errors.UndecodableBytesDefect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email.errors", "mro": ["email.errors.UndecodableBytesDefect", "email.errors.MessageDefect", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.errors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.errors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.errors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.errors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.errors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email.errors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\email\\errors.pyi"}