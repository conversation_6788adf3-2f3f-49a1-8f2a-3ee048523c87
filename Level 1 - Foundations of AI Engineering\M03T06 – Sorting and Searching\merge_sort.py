# Python script changes for merge sort algorithm from example in notes

def merge_sort(items):
    items_length = len(items)
    temporary_storage = [None] * items_length
    size_of_subsections = 1

    while size_of_subsections < items_length:
        for i in range(0, items_length, size_of_subsections * 2):
            first_section_start, first_section_end = i, min(
                i + size_of_subsections, items_length
            )
            second_section_start, second_section_end = first_section_end, min(
                first_section_end + size_of_subsections, items_length
            )

            sections = (first_section_start, first_section_end), (
                second_section_start,
                second_section_end,
            )

            merge(items, sections, temporary_storage)

        size_of_subsections *= 2

    return items


def merge(items, sections, temporary_storage):
    (first_section_start, first_section_end), (
        second_section_start,
        second_section_end,
    ) = sections

    left_index = first_section_start
    right_index = second_section_start
    temp_index = 0

    while left_index < first_section_end or right_index < second_section_end:
        if left_index < first_section_end and right_index < second_section_end:
            if len(items[left_index]) > len(items[right_index]):
                temporary_storage[temp_index] = items[left_index]
                left_index += 1
            else:
                temporary_storage[temp_index] = items[right_index]
                right_index += 1
            temp_index += 1

        elif left_index < first_section_end:
            for i in range(left_index, first_section_end):
                temporary_storage[temp_index] = items[left_index]
                left_index += 1
                temp_index += 1

        else:
            for i in range(right_index, second_section_end):
                temporary_storage[temp_index] = items[right_index]
                right_index += 1
                temp_index += 1

    for i in range(temp_index):
        items[first_section_start + i] = temporary_storage[i]


test_list_1 = ["Black Mamba", "Boomslang", "Puff Adder", "Cape Cobra", "Mozambique Spitting Cobra", "Rinkhals", "Southern African Rock Python", "Brown House Snake", "Southern Twig Snake", "Eastern Green Mamba"]
print("Test List 1:")
print("Original:", test_list_1)
sorted_list_1 = merge_sort(test_list_1.copy())
print("Sorted:", sorted_list_1)
print()

test_list_2 = ["Lion", "Leopard", "Elephant", "Rhino", "Buffalo", "Giraffe", "Cheetah", "Wild Dog", "Hyena", "Zebra"]
print("Test List 2:")
print("Original:", test_list_2)
sorted_list_2 = merge_sort(test_list_2.copy())
print("Sorted:", sorted_list_2)
print()

test_list_3 = ["Siya Kolisi", "Eben Etzebeth", "Cheslin Kolbe", "Handrè Pollard", "Willie le Roux", "Pieter-Steph du Toit", "Damian de Allende", "Lukhanyo Am", "Faf de Klerk", "Malcolm Marx"]
print("Test List 3:")
print("Original:", test_list_3)
sorted_list_3 = merge_sort(test_list_3.copy())
print("Sorted:", sorted_list_3)