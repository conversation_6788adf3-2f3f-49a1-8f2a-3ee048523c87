# Create a Python script that manages a list of music albums.


class Album:
    def __init__(self, album_name, album_artist, number_of_songs):
        self.album_name = album_name
        self.number_of_songs = number_of_songs
        self.album_artist = album_artist

    def __str__(self):
        return f"({self.album_name}, {self.album_artist}, {self.number_of_songs})"
    
albums1 = [ Album("Mylo Xyloto", "Coldplay", 10),
                Album("Late Night People", "GoldFish", 12), 
                Album("Country Grammer", "Nelly", 13), 
                Album("The Eminem Show", "Eminem", 18), 
                Album("The College Dropout", "Kanye West", 12) ]

print("Albums one list:")
for album in albums1:
    print(album)

albums1.sort(key=lambda album: album.number_of_songs)
print("\nAlbums one sorted list of number of songs:")
for album in albums1:
    print(album)

albums1[0], albums1[1] = albums1[1], albums1[0]
print("\nAlbums one first two albums swapped around:")
for album in albums1:
    print(album)

albums2 = [ Album("Wanted", "Bow Wow", 15),
                Album("J.Lo", "Jennifer Lopez", 16), 
                Album("Sweet Dreams", "Fabolous", 12), 
                Album("Jackpot", "Chingy", 10), 
                Album("The Documentary", "The Game", 14) ]

print("\nAlbums two list:")
for album in albums2:
    print(album)

albums2.extend(albums1)

albums2.append(Album("Dark Side of the Moon", "Pink Floyd", 9))
albums2.append(Album("oops... I did it again", "Britney Spears", 16))

albums2.sort(key=lambda album: album.album_name)
print("\nAlbums two sorted list of album names:")
for album in albums2:
    print(album)

for i, album in enumerate(albums2):
    if album.album_name == "Dark Side of the Moon":
        print(f"\nDark Side of the Moon is at index {i} in the list.")
        break