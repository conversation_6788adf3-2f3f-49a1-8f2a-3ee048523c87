# =====================================================================
# Task 1: Album Management System
# M03T06 – Sorting and Searching
# =====================================================================

# Step 1: Create the Album class
class Album:
    """
    A class to represent a music album.
    
    This class stores information about an album including:
    - The album name
    - The artist who created it  
    - The number of songs on the album
    """
    
    def __init__(self, album_name, album_artist, number_of_songs):
        """
        Constructor method - this runs when we create a new Album object.
        
        Parameters:
        album_name (str): The name of the album
        album_artist (str): The artist who made the album
        number_of_songs (int): How many songs are on the album
        """
        # Store the information in instance variables
        # Each Album object will have its own copy of these variables
        self.album_name = album_name
        self.album_artist = album_artist
        self.number_of_songs = number_of_songs
    
    def __str__(self):
        """
        This method tells Python how to display an Album object when we print it.
        It returns a string in the format: (album_name, album_artist, number_of_songs)
        """
        return f"({self.album_name}, {self.album_artist}, {self.number_of_songs})"


# Let's test our Album class to make sure it works
print("=== Testing the Album Class ===")

# Create a test album
test_album = Album("Thriller", "Michael Jackson", 9)

# Print it to see if our __str__ method works
print("Test album:", test_album)

# Access individual attributes
print("Album name:", test_album.album_name)
print("Artist:", test_album.album_artist) 
print("Number of songs:", test_album.number_of_songs)

print("\n" + "="*50)
print("Album class created successfully!")
print("Ready for Step 2...")
